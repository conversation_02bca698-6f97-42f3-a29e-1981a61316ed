using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.AddDbContext<TodoDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));
builder.Services.AddScoped<ITodoService, TodoService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

// TodoList CRUD API
app.MapGet("/todos", (ITodoService todoService) =>
{
    return todoService.GetAllTodos();
})
.WithName("GetAllTodos")
.WithOpenApi();

app.MapGet("/todos/{id}", (ITodoService todoService, int id) =>
{
    var todo = todoService.GetTodoById(id);
    return todo != null ? Results.Ok(todo) : Results.NotFound();
})
.WithName("GetTodoById")
.WithOpenApi();

app.MapGet("/todos/search", (ITodoService todoService, string? title, string? description) =>
{
    var todos = todoService.SearchTodos(title, description);
    return Results.Ok(todos);
})
.WithName("SearchTodos")
.WithOpenApi();

app.MapPost("/todos", (ITodoService todoService, CreateTodoRequest request) =>
{
    try
    {
        var todo = todoService.CreateTodo(request.Title, request.Description);
        return Results.Created($"/todos/{todo.Id}", todo);
    }
    catch (ArgumentException ex)
    {
        return Results.BadRequest(new { error = ex.Message });
    }
})
.WithName("CreateTodo")
.WithOpenApi();

app.MapPut("/todos/{id}", (ITodoService todoService, int id, UpdateTodoRequest request) =>
{
    var updated = todoService.UpdateTodo(id, request.Title, request.Description, request.IsCompleted);
    return updated != null ? Results.Ok(updated) : Results.NotFound();
})
.WithName("UpdateTodo")
.WithOpenApi();

app.MapDelete("/todos/{id}", (ITodoService todoService, int id) =>
{
    var deleted = todoService.DeleteTodo(id);
    return deleted ? Results.NoContent() : Results.NotFound();
})
.WithName("DeleteTodo")
.WithOpenApi();

app.Run();

public class TodoItem
{
    [Key]
    public int Id { get; set; }
    
    [Required]
    [MaxLength(200)]
    public string Title { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(1000)]
    public string Description { get; set; } = string.Empty;
    
    public bool IsCompleted { get; set; }
    
    public DateTime CreatedAt { get; set; }
    
    public DateTime? UpdatedAt { get; set; }
}

public record CreateTodoRequest(string Title, string Description);

public record UpdateTodoRequest(string? Title, string? Description, bool? IsCompleted);

public interface ITodoService
{
    IEnumerable<TodoItem> GetAllTodos();
    TodoItem? GetTodoById(int id);
    IEnumerable<TodoItem> SearchTodos(string? title = null, string? description = null);
    TodoItem CreateTodo(string title, string description);
    TodoItem? UpdateTodo(int id, string? title, string? description, bool? isCompleted);
    bool DeleteTodo(int id);
}

public class TodoDbContext : DbContext
{
    public TodoDbContext(DbContextOptions<TodoDbContext> options) : base(options) { }
    
    public DbSet<TodoItem> Todos { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<TodoItem>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedOnAdd();
            entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Description).IsRequired().HasMaxLength(1000);
            entity.Property(e => e.IsCompleted).HasDefaultValue(false);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        });
    }
}

public class TodoService : ITodoService
{
    private readonly TodoDbContext _context;

    public TodoService(TodoDbContext context)
    {
        _context = context;
    }

    public IEnumerable<TodoItem> GetAllTodos() => _context.Todos.ToList();

    public TodoItem? GetTodoById(int id) => _context.Todos.FirstOrDefault(t => t.Id == id);
    
    public IEnumerable<TodoItem> SearchTodos(string? title = null, string? description = null)
    {
        var query = _context.Todos.AsQueryable();
        
        if (!string.IsNullOrWhiteSpace(title))
            query = query.Where(t => t.Title.Contains(title));
            
        if (!string.IsNullOrWhiteSpace(description))
            query = query.Where(t => t.Description.Contains(description));
            
        return query.ToList();
    }

    public TodoItem CreateTodo(string title, string description)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be null or empty", nameof(title));
        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Description cannot be null or empty", nameof(description));
            
        var todo = new TodoItem
        {
            Title = title,
            Description = description,
            IsCompleted = false,
            CreatedAt = DateTime.UtcNow
        };
        
        _context.Todos.Add(todo);
        _context.SaveChanges();
        return todo;
    }

    public TodoItem? UpdateTodo(int id, string? title, string? description, bool? isCompleted)
    {
        var todo = GetTodoById(id);
        if (todo == null) return null;

        if (!string.IsNullOrWhiteSpace(title))
            todo.Title = title;
        if (!string.IsNullOrWhiteSpace(description))
            todo.Description = description;
        if (isCompleted.HasValue)
            todo.IsCompleted = isCompleted.Value;
            
        todo.UpdatedAt = DateTime.UtcNow;
        
        _context.SaveChanges();
        return todo;
    }

    public bool DeleteTodo(int id)
    {
        var todo = GetTodoById(id);
        if (todo == null) return false;
        
        _context.Todos.Remove(todo);
        _context.SaveChanges();
        return true;
    }
}
